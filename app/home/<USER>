'use client';

import React, { useEffect } from 'react';
import ComponentAIChatInterface from '@/component/ai/ComponentAIChatInterface';
import ComponentInsightsCarousel from '@/component/dashboard/ComponentInsightsCarousel';
import { useCompanyContext } from '@/component/auth/ComponentRouteProtection';
import WelcomeHero from '@/component/widgets/WelcomeHero';

export default function HomePage() {

    const { hasCompany, retry } = useCompanyContext();

    useEffect(() => {
        if (!hasCompany) {
            retry();
        }
    }, [hasCompany, retry]);

    return (
        <div className="h-screen flex flex-col max-w-6xl mx-auto">
            <div className="mt-10 item-center">
                <WelcomeHero userName="John Doe" companyName="Acme Inc." />
            </div>
            <div className="max-w-5xl mx-auto mt-10 border rounded-lg border-gray-200">
                <div className="">
                    <ComponentAIChatInterface className="" />
                </div>
                {/* <div className="rounded-lg">
                    <div className="h-full overflow-hidden">
                        <ComponentInsightsCarousel />
                    </div>
                </div> */}
            </div>
        </div>
    );
}
