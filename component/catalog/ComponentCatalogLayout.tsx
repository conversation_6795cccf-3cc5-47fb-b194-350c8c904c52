'use client';

import React from 'react';
import SecondaryNavbar, { SecondaryNavbarLayout } from "@/component/common/ComponentSecondaryNavbar";
import { SecondaryNavItem } from "@/types/component/common/TypeSecondaryNavbar";
import { Package, Quote, Receipt, ReceiptText  } from "lucide-react";


export interface CatalogLayoutProps {
    children: React.ReactNode;
}

const navItems: SecondaryNavItem[] = [
    {
        id: 'products',
        label: 'Products',
        icon: <Package className="h-5 w-5" />,
        href: `/catalog`,
    },
    {
        id: 'proposals',
        label: 'Proposals',
        icon: <ReceiptText className="h-5 w-5" />,
        href: `/catalog/quotes`,
    },
];

const ComponentCatalogLayout: React.FC<CatalogLayoutProps> = ({ children }) => {
    return (
        <div className="max-w-6xl mx-auto mb-10 mt-10">
            <div className='mb-10'>
                <SecondaryNavbarLayout
                    navbar={
                        <SecondaryNavbar
                            items={navItems}
                        />
                    }
                    content={children}
                />
            </div>
        </div>
    );
}

export default ComponentCatalogLayout; 