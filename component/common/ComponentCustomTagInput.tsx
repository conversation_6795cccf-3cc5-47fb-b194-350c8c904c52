import React from 'react';
import { Tag, Info, Trash2, Plus } from 'lucide-react';

export type CustomTagInput = {
    id?: string;
    label: string;
    key: string;
    value: string;
    type: string;
    description?: string;
};

type Props = {
    value: CustomTagInput;
    onChange: (tag: CustomTagInput) => void;
    error?: string;
};

const ComponentCustomTagInput: React.FC<Props> = ({ value, onChange, error }) => {
    return (
        <div className="space-y-3 mt-4">
            <label className="block relative">
                <span className="absolute left-2 top-2 text-gray-400 pointer-events-none">
                    <Tag className="h-4 w-4" />
                </span>
                <input
                    type="text"
                    placeholder="Label (eg: deal size)"
                    value={value.label}
                    onChange={e => onChange({ ...value, label: e.target.value })}
                    className="w-full p-2 pl-8 border border-gray-300 rounded-lg text-sm"
                />
            </label>

            <label className="block relative">
                <span className="absolute left-2 top-2 text-gray-400 pointer-events-none">
                    <Tag className="h-4 w-4" />
                </span>
                <input
                    type="text"
                    placeholder="Value (eg: 100,000$)"
                    value={value.value}
                    onChange={e => onChange({ ...value, value: e.target.value })}
                    className="w-full p-2 pl-8 border border-gray-300 rounded-lg text-sm"
                />
            </label>


            {/* <label className="block relative">
                <span className="absolute left-2 top-2 text-gray-400 pointer-events-none">
                    <Info className="h-4 w-4" />
                </span>
                <input
                    type="text"
                    placeholder="Description (optional)"
                    value={value.description || ''}
                    onChange={e => onChange({ ...value, description: e.target.value })}
                    className="w-full p-2 pl-8 border border-gray-300 rounded-lg text-sm"
                />
            </label> */}
            {error && <div className="text-red-500 text-xs mt-1">{error}</div>}
        </div>
    );
};

// New: Multi-tag management component

export type CustomTagsInputProps = {
    value?: CustomTagInput[];
    onTagsChange?: (tags: CustomTagInput[]) => void;
    error?: string;
};

const defaultTag: CustomTagInput = { label: '', key: '', value: '', type: '', description: '' };

export const ComponentCustomTagsInput: React.FC<CustomTagsInputProps> = ({ value = [], onTagsChange, error }) => {
    // Use value prop directly instead of internal state for better form integration
    const tags = value;

    const handleAddTag = () => {
        const newTags = [...tags, { ...defaultTag }];
        onTagsChange?.(newTags);
    };

    const handleRemoveTag = (idx: number) => {
        const newTags = tags.filter((_, i) => i !== idx);
        onTagsChange?.(newTags);
    };

    const handleTagChange = (idx: number, tag: CustomTagInput) => {
        const updatedTag = {
            ...tag,
            key: tag.label.trim().toLowerCase().replace(/\s+/g, '_'),
            type: 'STRING'
        }
        const newTags = [...tags];
        newTags[idx] = updatedTag;
        onTagsChange?.(newTags);
    };

    return (
        <div>
            <div className="flex gap-2 mb-2 items-center">
                <h3 className="text-lg font-semibold text-gray-900">Custom Tags</h3>
                <button type="button" onClick={handleAddTag} className="text-white hover:bg-opacity-90 text-xs bg-primary p-1 rounded-full cursor-pointer"><Plus className='h-4 w-4' /></button>
            </div>
            <div className="text-gray-500 text-sm mb-4">Add custom tags to categorize or describe your product.</div>
            <div className={tags.length > 0 ? "grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4" : ""}>
                {tags.length > 0 ? (
                    tags.map((tag, idx) => (
                        <div key={idx} className="relative border border-gray-100 rounded-lg p-4 min-h-[120px]">
                            <ComponentCustomTagInput
                                value={tag}
                                onChange={t => handleTagChange(idx, t)}
                            />
                            <button
                                type="button"
                                onClick={() => handleRemoveTag(idx)}
                                className="mt-2 bg-red-500/30 text-red-500 hover:bg-opacity-90 cursor-pointer rounded-md p-1 transition-colors w-full"
                                aria-label="Remove Tag"
                            >
                                <div className='flex items-center gap-2 justify-center'>
                                    <Trash2 className='h-3 w-3' /> Delete
                                </div>
                            </button>
                        </div>
                    ))
                ) : (
                    <div className="border border-dashed border-gray-300 rounded-lg p-4 text-center text-gray-400 col-span-4">
                        No custom tags added yet.
                    </div>
                )}
            </div>
        </div>
    );
};

export default ComponentCustomTagInput; 